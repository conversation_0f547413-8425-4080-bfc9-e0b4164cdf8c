<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update existing completed todos to have a completion timestamp
        DB::table('todos')->where('completed', 1)->update(['completed' => now()]);

        // Then change the column type from boolean to timestamp
        Schema::table('todos', function (Blueprint $table) {
            $table->timestamp('completed')->nullable()->change();
        });

        // Set NULL for todos that were not completed (completed = 0)
        DB::table('todos')->where('completed', 0)->update(['completed' => null]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Update the data first: any timestamp becomes true, null becomes false
        DB::table('todos')->whereNotNull('completed')->update(['completed' => 1]);
        DB::table('todos')->whereNull('completed')->update(['completed' => 0]);

        Schema::table('todos', function (Blueprint $table) {
            // Convert timestamps back to boolean
            $table->boolean('completed')->default(false)->change();
        });
    }
};
