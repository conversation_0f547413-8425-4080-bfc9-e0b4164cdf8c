# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
docs/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Laravel
/storage/*.key
/storage/logs/*
!/storage/logs/.gitkeep
/storage/framework/cache/*
!/storage/framework/cache/.gitkeep
/storage/framework/sessions/*
!/storage/framework/sessions/.gitkeep
/storage/framework/testing/*
!/storage/framework/testing/.gitkeep
/storage/framework/views/*
!/storage/framework/views/.gitkeep
/storage/app/public/*
!/storage/app/public/.gitkeep
/bootstrap/cache/*
!/bootstrap/cache/.gitkeep

# Environment files
.env
.env.*
!.env.example
!.env.docker

# Composer
/vendor/

# Testing
/coverage/
.phpunit.result.cache

# Build artifacts
/public/build/
/public/hot
/public/storage

# Database
database/database.sqlite
*.sql

# Logs
*.log

# Cache
.cache/

# Temporary files
*.tmp
*.temp

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Development tools
phpunit.xml
.phpunit.cache
.pest/
