[PHP]
; Performance
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
max_input_vars = 3000

; File uploads
upload_max_filesize = 100M
post_max_size = 100M
file_uploads = On

; Error reporting
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT

; Session
session.save_handler = redis
session.save_path = "tcp://redis:6379"
session.gc_maxlifetime = 7200
session.cookie_lifetime = 0
session.cookie_secure = Off
session.cookie_httponly = On
session.use_strict_mode = On

; Security
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; OPcache
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 20000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
opcache.validate_timestamps = 1

; Realpath cache
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; Date
date.timezone = UTC

; Logging
log_errors_max_len = 1024

; Other
default_charset = "UTF-8"
short_open_tag = Off
