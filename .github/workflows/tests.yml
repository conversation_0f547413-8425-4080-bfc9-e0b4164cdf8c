name: Tests

on:
  push:
    branches:
      - develop
      - main
  pull_request:
    branches:
      - develop
      - main

jobs:
  tests:
    name: Run Tests
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-version: [8.2, 8.3, 8.4]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP ${{ matrix.php-version }}
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          tools: composer:v2
          coverage: xdebug
          extensions: pdo, sqlite, pdo_sqlite

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: vendor
          key: composer-${{ runner.os }}-${{ matrix.php-version }}-${{ hashFiles('composer.lock') }}
          restore-keys: |
            composer-${{ runner.os }}-${{ matrix.php-version }}-
            composer-${{ runner.os }}-

      - name: Install Node.js dependencies
        run: npm ci

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Copy environment file
        run: cp .env.example .env

      - name: Generate application key
        run: php artisan key:generate

      - name: Create SQLite database
        run: touch database/database.sqlite

      - name: Run database migrations
        run: php artisan migrate --force

      - name: Publish Ziggy configuration
        run: php artisan ziggy:generate

      - name: Build frontend assets
        run: npm run build

      - name: Run tests
        run: php artisan test --coverage --min=80

      - name: Upload coverage reports to Codecov
        if: matrix.php-version == '8.4'
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage.xml
          fail_ci_if_error: false
